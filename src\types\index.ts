
export type FileItem = {
  fileName: string;
  lastModified: string,
  user?: string,
  conversationId?: number,
  id?: number
};

export type Conversation = {
  id: number,
  agentId: number,
  agentRole: number,
  ticketId: number,
  topic: string,
  status: string,
  difyId: string,
}

export type Agent = {
  code: string,
  moduleId: number,
  name: string,
  description: string,
  type: string,
  status: boolean,
  isDelete: boolean,
  id: number,
  createdBy: string,
  createdAt: string,
  updatedBy: string,
  updatedAt: string
}

export type TicketAction = {
   statusId: number,
   statusCode: string,
   stepId: number
}
export type TicketPagination = {
  data: Ticket[],
  totalCount: number,
  pageIndex: number,
  pageSize: number 
}

export type Ticket = {
  id: number,
  moduleId: number,
  moduleName:string,
  overallStatus: string,
  approvalFlowId: number,
  title: string,
  description?: string,
  approvalFlowInfo: any,
  createdBy: string,
  createdAt: string,
  ticketStatuses: any[]
}

export type TicketDetail = {
  id: number,
  moduleId: number,
  moduleName:string,
  overallStatus: string,
  approvalFlowId: number,
  title: string,
  description?: string,
  approvalFlowInfo: any,
  createdBy: string,
  createdAt: string,
  ticketStatuses: TicketApprovalStatus[]
}

export type TicketApprovalStatus = {
  id: number,
  stepOrder: number,
  name: string,
  userId: string,
  status: string,
  updatedBy: string,
  updatedAt: string,
  createdAt: string,
  approvedBy: string,
  additionInfos: AdditionInfo[]
}

export type AdditionInfo = {
  id: number,
  ticketId: number,
  stepId: number,
  key: string,
  type: string,
  value: string
}

export type ChatMessage = {
  // id: number,
  id: string,
  query: string,
  answer: string,
  conversationId: number,
  createTicket: boolean,
  form?: any,
  // created_at: number
}

export type ChatHistory = {
  hasMore: boolean,
  data: ChatMessage[]
}

export type ParamItem = {
  [key: string]: {
    label: string;
    variable: string;
    required: boolean;
    default: any;
    type: string;
    max_length: number;
    options: string[];
    allowed_file_upload_methods?: string[] | null;
    allowed_file_types?: string[] | null;
    allowed_file_extensions?: string[] | null;
  };
};

export type ConversationParamItem = {
  id: number,
  conversationId: number,
  paramKey: string,
  paramValue: string
}

export type ServiceItem = {
  key: string,
  value: string,
  options: string[],
  question: string
}


export type UserRole = {
  type: number,
  code: string
}

export type UserFunc = {
  items: UserFuncItem[],
  id: number,
  code: string,
  description: string | null,
  displayName: string,
  parentCode: string | null,
  appCode: string,
  orders: string,
  host: string | null,
  prefix: string | null,
  path: string,
  cssClass: string | null,
  isEnable: boolean | null
}

export type UserFuncItem = {
  items: UserFuncItem[],
  id: number,
  code: string,
  description: string | null,
  displayName: string,
  parentCode: string,
  appCode: string,
  orders: string,
  host: string | null,
  prefix: string | null,
  path: string,
  cssClass: string | null,
  isEnable: boolean | null
}

export type User = {
  id: number,
  userId: number,
  userName: string,
  fullName: string,
  email: string,
  phone: string,
  positionName: string | null,
  address: string | null,
  departmentName: string | null,
  departmentCode: string | null,
  divisionName: string | null,
  divisionCode: string,
  positionCode: string,
  manager: string | null,
  roles: UserRole[],
  funcs: UserFunc[]
}

export type LoginResponse = {
  user: User,
  token: string
}

export type ModuleApp = {
   id: number,
   name: string,
   description: string,
   enabled: boolean,
   createdBy: string,
   createdAt: string,
   approvalFlowId: number
}

export type ModuleAppPagination = {
  data: ModuleApp[],
  totalCount: number,
  pageIndex: number,
  pageSize: number 
}

export interface AdditionInfoSetting {
  id: number;
  stepId: number;
  fieldName: string;
  type: string;
  isRequired: boolean
}

export interface StepApprover {
  id: number;
  stepId: number;
  userId: string;
  type: string;
  name: string;
  additionInfoSettings: AdditionInfoSetting[];
}

export interface Step {
  flowId: number;
  stepOrder: number;
  name: string;
  stepApprovers: StepApprover[];
}

export interface CreateFlowRequest {
  name: string;
  description: string;
  moduleId: number;
  steps: Step[];
}

export interface UpdateFlowRequest {
  id: number;
  name: string;
  description: string;
  moduleId: number;
  steps: Step[];
}

export interface ApprovalFlow {
  id: number;
  name: string;
  description: string;
  moduleId: number;
  steps: Step[];
  createdBy: string;
  createdAt: string;
  updatedBy: string;
  updatedAt: string;
}

export interface AddAdditionInfoSettingRequest {
  stepId: number;
  additionInfos: AdditionInfoSetting[];
}

export interface UpdateAdditionInfoSettingRequest {
  id: number;
  fieldName: string;
  type: string;
  isRequired: boolean
}