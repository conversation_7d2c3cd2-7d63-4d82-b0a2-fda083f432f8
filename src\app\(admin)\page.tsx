"use client";

import React, { useEffect, useState, useRef } from "react";
import { ticketApi } from "@/api/ticketApi";
import { agentApi } from "@/api/agentApi";
import { useRouter } from "next/navigation";
import {
  FaTicketAlt,
  FaUsers,
  FaRobot,
  FaChartLine,
  FaClock,
  FaCheckCircle,
  FaExclamationTriangle,
  FaTimesCircle,
  FaPaperPlane
} from "react-icons/fa";
import { <PERSON>E<PERSON>, FiUser, FiTool, FiCalendar, FiEdit3, FiClock } from "react-icons/fi";
import Link from "next/link";
import {
  Table,
  TableBody,
  TableCell,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import Badge from "@/components/ui/badge/Badge";
import { conversationApi } from "@/api/conversationApi";
import { ChatMessage } from "@/types";
import MarkdownViewer from "@/components/markdownviewer/MarkdownViewer";
import "./dashboard-animations.css";

const statusColor: Record<string, string> = {
  'Chờ xử lý': 'bg-amber-50 text-amber-700 border-amber-200',
  '<PERSON>ang xử lý': 'bg-blue-50 text-blue-700 border-blue-200',
  'Đã đóng': 'bg-emerald-50 text-emerald-700 border-emerald-200',
  'Bị từ chối': 'bg-red-50 text-red-700 border-red-200',
  'Đã hủy': 'bg-gray-50 text-gray-700 border-gray-200',
  'Hoàn thành': 'bg-green-50 text-green-700 border-green-200',
  undefined: 'bg-gray-50 text-gray-700 border-gray-200',
  null: 'bg-gray-50 text-gray-700 border-gray-200',
  '': 'bg-gray-50 text-gray-700 border-gray-200'
};

const statusIcons: Record<string, React.ReactNode> = {
  'Chờ xử lý': <FiClock className="w-3 h-3" />,
  'Đang xử lý': <div className="w-3 h-3 rounded-full bg-blue-500"></div>,
  'Đã đóng': <div className="w-3 h-3 rounded-full bg-emerald-500"></div>,
  'Bị từ chối': <div className="w-3 h-3 rounded-full bg-red-500"></div>,
  'Đã hủy': <div className="w-3 h-3 rounded-full bg-gray-500"></div>,
  'Hoàn thành': <div className="w-3 h-3 rounded-full bg-green-500"></div>,
  undefined: <FiClock className="w-3 h-3" />,
  null: <FiClock className="w-3 h-3" />,
  '': <FiClock className="w-3 h-3" />
};

// Component thẻ thống kê
interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  color: string;
  trend?: {
    value: string;
    isPositive: boolean;
  };
}

function StatCard({ title, value, icon, color, trend }: StatCardProps) {
  const [animatedValue, setAnimatedValue] = useState(0);

  useEffect(() => {
    const timer = setTimeout(() => {
      let start = 0;
      const end = typeof value === 'number' ? value : parseInt(value.toString()) || 0;
      const duration = 1500; // Tăng thời gian để mượt hơn
      const increment = end / (duration / 16);

      const counter = setInterval(() => {
        start += increment;
        if (start >= end) {
          setAnimatedValue(end);
          clearInterval(counter);
        } else {
          setAnimatedValue(Math.floor(start));
        }
      }, 16);

      return () => clearInterval(counter);
    }, 300); // Delay khác nhau cho mỗi card

    return () => clearTimeout(timer);
  }, [value]);

  return (
    <div className="group relative bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-600 p-6 shadow-sm hover:shadow-xl hover:scale-[1.03] transition-all duration-500 overflow-hidden ">
      {/* Background gradient effect */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50/20 to-purple-50/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

      {/* Floating particles effect */}
      <div className="absolute top-3 right-3 w-2 h-2 bg-blue-400 rounded-full opacity-0 group-hover:opacity-60 group-hover:animate-ping transition-all duration-500"></div>
      <div className="absolute bottom-4 left-4 w-1.5 h-1.5 bg-purple-400 rounded-full opacity-0 group-hover:opacity-40 group-hover:animate-pulse transition-all duration-700"></div>

      {/* Shimmer effect */}
      <div className="absolute inset-0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 bg-gradient-to-r from-transparent via-white/30 to-transparent"></div>

      <div className="relative flex items-center justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-600 group-hover:text-gray-700 transition-colors duration-300 dark:text-gray-50">{title}</p>
          <p className="text-3xl font-bold text-gray-900 mt-2 group-hover:text-blue-600 transition-colors duration-300 dark:text-gray-50">
            {animatedValue.toLocaleString()}
          </p>
          {/* {trend && (
            <div className={`flex items-center mt-3 text-sm font-medium transition-all duration-300 ${
              trend.isPositive ? 'text-green-600' : 'text-red-600'
            }`}>
              <span className={`inline-flex items-center justify-center w-5 h-5 rounded-full mr-2 transition-all duration-300 ${
                trend.isPositive ? 'bg-green-100 group-hover:bg-green-200 group-hover:scale-110' : 'bg-red-100 group-hover:bg-red-200 group-hover:scale-110'
              }`}>
                {trend.isPositive ? '↗' : '↘'}
              </span>
              <span className="group-hover:font-semibold transition-all duration-300">{trend.value}</span>
              <span className="text-gray-500 ml-1 group-hover:text-gray-600 transition-colors duration-300">vs last month</span>
            </div>
          )} */}
        </div>
        <div className={`relative p-4 rounded-xl ${color} group-hover:scale-110 group-hover:rotate-3 transition-all duration-500 shadow-lg group-hover:shadow-2xl`}>
          {/* Icon glow effect */}
          <div className="absolute inset-0 bg-white/20 rounded-xl opacity-0 group-hover:opacity-100 group-hover:animate-pulse transition-all duration-500"></div>

          {/* Rotating ring */}
          <div className="absolute inset-0 border-2 border-white/30 rounded-xl opacity-0 group-hover:opacity-100 group-hover:animate-spin transition-all duration-500 dark:text-gray-50" style={{animationDuration: '3s'}}></div>

          <div className="relative group-hover:scale-110 transition-transform duration-300">
            {icon}
          </div>
        </div>
      </div>
    </div>
  );
}

// Component Agent Card
interface AgentCardProps {
  agent: any;
  onClick: () => void;
}

function AgentCard({ agent, onClick }: AgentCardProps) {
  return (
    <div
      className="group relative bg-gradient-to-br dark:from-blue-50/10 dark:to-blue-50/15 from-white to-blue-50/20 rounded-xl border border-gray-200 dark:border-gray-600 p-4 hover:shadow-lg hover:scale-[1.02] transition-all duration-500 cursor-pointer hover:border-blue-300"
      onClick={onClick}
    >
      {/* Subtle background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>

      <div className="relative flex items-center space-x-3">
        <div className="relative w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center transition-transform duration-500 shadow-lg group-hover:shadow-xl">
          <FaRobot className="text-white text-lg transition-transform duration-300 group-hover:scale-110" />
        </div>

        <div className="flex-1 min-w-0">
          <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 group-hover:text-blue-600 truncate transition-colors duration-300">
            {agent.name}
          </h3>
          <p className="text-xs text-gray-500 group-hover:text-gray-600 truncate transition-colors duration-300 dark:text-gray-200">
            {agent.description}
          </p>
          <div className="flex items-center mt-2">
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 group-hover:bg-green-200 transition-colors duration-300">
              <span className="w-1.5 h-1.5 bg-green-400 rounded-full mr-1.5"></span>
              Hoạt động
            </span>
          </div>
        </div>

        {/* Arrow indicator */}
        <div className="opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-x-1 group-hover:translate-x-0">
          <svg className="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </div>
      </div>
    </div>
  );
}

// Component AI Chat
interface AIChatProps {
  className?: string;
}

function AIChat({ className }: AIChatProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [input, setInput] = useState('');
  const [conversationId, setConversationId] = useState(0);
  const chatRef = useRef<HTMLDivElement>(null);
  const agentCode = process.env.NEXT_PUBLIC_AGENT_CODE_DASHBOARD || "TICKETSUPPORT";

  useEffect(() => {
    chatRef.current?.scrollTo({ top: chatRef.current.scrollHeight, behavior: 'smooth' });
  }, [messages]);

  const handleSend = async () => {
    if (!input.trim()) return;

    const inputValue = input;
    setMessages(prev => [...prev, {
      "query": input,
      "answer": "(thinking)",
      "conversationId": conversationId,
      "createTicket": false,
      "id": ""
    }]);
    setInput("");

    try {
      const data = await conversationApi.sendMessage(inputValue, conversationId, [], agentCode, {});
      if (data != null) {
        if (data.conversationId) {
          setConversationId(data.conversationId);
        }
        setMessages(prevMessages => {
          const lastIndex = prevMessages.length - 1;
          if (prevMessages[lastIndex]?.answer === '(thinking)') {
            const updatedMessages = [...prevMessages];
            updatedMessages[lastIndex].answer = data.answer ?? "Hệ thống đang suy nghĩ...";
            return updatedMessages;
          }
          return prevMessages;
        });
      }
    } catch (err) {
      console.error("Error sending message:", err);
    }
  };

  return (
    <div className={`relative h-[750px] bg-white dark:bg-gray-800 rounded-xl border border-gray-200  dark:border-gray-600 shadow-sm hover:shadow-xl hover:shadow-sky-100/50 transition-all duration-500 flex flex-col ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-600 bg-gradient-to-r from-sky-50/10 to-blue-50/5">
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 bg-gradient-primary rounded-full flex items-center justify-center">
            <FaRobot className="text-white text-xl" />
          </div>
          <div>
            <h2 className="text-lg font-semibold text-sky-700 dark:text-gray-100">
              AI Assistant
            </h2>
            <div className="flex items-center text-sm text-sky-600 dark:text-gray-100">
              <span className="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
              <span>Đang trực tuyến</span>
            </div>
          </div>
        </div>
      </div>

      {/* Chat Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4 h-full" ref={chatRef}>
        {messages.length === 0 && (
          <div className="text-center text-gray-500 py-8">
            <FaRobot className="text-4xl text-gray-300 mx-auto mb-4" />
            <p className="text-sm">Xin chào! Tôi có thể giúp gì cho bạn? 👋</p>
          </div>
        )}

        {messages.map((message, index) => (
          <div key={index} className="space-y-3">
            {/* User message */}
            <div className="flex justify-end">
              <div className="bg-gradient-primary text-white rounded-2xl rounded-br-md px-4 py-3 max-w-xs shadow-sky-200">
                <p className="text-sm">{message.query}</p>
              </div>
            </div>

            {/* AI response */}
            {message.answer && message.answer !== '(thinking)' && (
              <div className="flex">
                <div className="bg-gradient-to-r from-sky-50 to-blue-50 rounded-2xl rounded-bl-md px-4 py-3 max-w-xs-">
                  <MarkdownViewer content={message.answer} className="dark:text-gray-900" />
                </div>
              </div>
            )}

            {message.answer === '(thinking)' && (
              <div className="flex">
                <div className="bg-gradient-to-r from-sky-50 to-blue-50 rounded-2xl rounded-bl-md px-4 py-3 shadow-sm  ">
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 bg-sky-400 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                    <div className="w-2 h-2 bg-sky-600 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                  </div>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Input */}
      <div className="relative p-4 border-t border-gray-200 dark:border-gray-600 bg-gradient-to-r from-sky-50/10 to-blue-50/5">
        <div className="flex space-x-3">
          <div className="relative flex-1">
            <input
              type="text"
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && handleSend()}
              placeholder="Nhập tin nhắn..."
              className="w-full border border-gray-300 rounded-xl px-4 py-3 text-sm focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-transparent transition-all duration-300 bg-white/80 backdrop-blur-sm shadow-sm hover:shadow-md"
            />
            <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-sky-500/10 to-blue-500/10 opacity-0 focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
          </div>
          <button
            onClick={handleSend}
            className="bg-gradient-primary hover:bg-gradient-primary-hover text-white px-4 py-3 rounded-xl text-sm flex items-center hover:shadow-xl hover:scale-105 transition-all duration-300"
          >
            <FaPaperPlane className="transform hover:translate-x-0.5 transition-transform duration-300" />
          </button>
        </div>
      </div>
    </div>
  );
}

export default function Dashboard() {
  const [recentAgents, setRecentAgents] = useState<any[]>([]);
  const [recentTickets, setRecentTickets] = useState<any[]>([]);
  const [stats, setStats] = useState({
    totalTickets: 0,
    pendingTickets: 0,
    completedTickets: 0,
    activeAgents: 0
  });
  const [loading, setLoading] = useState(true);

  const router = useRouter();

  // Fetch dữ liệu thống kê và tickets
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);

        // Fetch agents
        const agents = await agentApi.getAgents(5, 0); // Lấy 5 agents gần đây
        setRecentAgents(agents);

        // Fetch tickets
        const tickets = await ticketApi.getTickets(undefined, undefined, undefined, 100, 0);

        // Lấy 10 tickets mới nhất
        const sortedTickets = tickets
          .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
          .slice(0, 10);

        setRecentTickets(sortedTickets);

        // Tính toán thống kê
        const totalTickets = tickets.length;
        const pendingTickets = tickets.filter(t => t.overallStatus === 'Chờ xử lý' || t.overallStatus === null || t.overallStatus === undefined).length;
        const completedTickets = tickets.filter(t => t.overallStatus === 'Đã đóng').length;
        const activeAgents = agents.length;

        setStats({
          totalTickets,
          pendingTickets,
          completedTickets,
          activeAgents
        });

      } catch (error) {
        console.error("Error fetching dashboard data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen p-6 relative">
      {/* Subtle background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-20 w-64 h-64 bg-sky-400/3 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 right-20 w-80 h-80 bg-blue-400/3 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 left-1/3 w-72 h-72 bg-sky-300/3 rounded-full blur-3xl"></div>
      </div>

      <div className="relative mx-auto space-y-6">
        {/* Header */}
        <div className="mb-8 text-center">
          <h1 className="text-4xl font-bold text-sky-700 dark:text-gray-300">
            Dashboard
          </h1>
          <p className="text-sky-600 mt-3 text-lg dark:text-gray-400">
            Chào mừng bạn đến với AI Portal Dashboard ✨
          </p>
        </div>

        {/* Thẻ thống kê */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="animate-fade-in" style={{animationDelay: '0.1s'}}>
            <StatCard
              title="Tổng số Tickets"
              value={stats.totalTickets}
              icon={<FaTicketAlt className="text-white text-xl group-hover:animate-bounce" />}
              color="bg-gradient-primary"
              trend={{ value: "+12%", isPositive: true }}
            />
          </div>
          <div className="animate-fade-in" style={{animationDelay: '0.2s'}}>
            <StatCard
              title="Tickets chờ xử lý"
              value={stats.pendingTickets}
              icon={<FaClock className="text-white text-xl group-hover:animate-spin" />}
              color="bg-gradient-warning"
              trend={{ value: "+5%", isPositive: true }}
            />
          </div>
          <div className="animate-fade-in" style={{animationDelay: '0.3s'}}>
            <StatCard
              title="Tickets hoàn thành"
              value={stats.completedTickets}
              icon={<FaCheckCircle className="text-white text-xl group-hover:animate-pulse" />}
              color="bg-gradient-success"
              trend={{ value: "+8%", isPositive: true }}
            />
          </div>
          <div className="animate-fade-in" style={{animationDelay: '0.4s'}}>
            <StatCard
              title="Agents hoạt động"
              value={stats.activeAgents}
              icon={<FaRobot className="text-white text-xl group-hover:animate-bounce" />}
              color="bg-gradient-secondary"
              trend={{ value: "+3%", isPositive: true }}
            />
          </div>
        </div>

        {/* Main Content - 2 Rows Layout */}
        <div className="space-y-6">
          {/* Row 1: Top 10 Tickets và AI Chat */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Top 10 Tickets mới nhất - 2 cột */}
            <div className="lg:col-span-2">
              <div className="group relative bg-white rounded-xl border border-gray-200  shadow-lg hover:shadow-xl hover:shadow-sky-100/50 transition-all overflow-auto duration-300 dark:border-gray-600 h-[750px]">
                {/* Subtle animated background */}
                {/* <div className="absolute inset-0 bg-gradient-to-br from-sky-50/30 via-blue-50/20 to-white opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div> */}

                <div className="relative p-6 border-b border-gray-200 bg-white dark:bg-gray-800 dark:border-gray-600 ">
                  <div className="flex items-center justify-between">
                    <h2 className="text-lg font-semibold text-sky-700 dark:text-gray-50">
                      🎫 Top 10 Tickets Mới Nhất
                    </h2>
                    <Link
                      href="/ticket"
                      className="group/link inline-flex items-center text-sky-600 dark:text-sky-400 hover:text-sky-700 text-sm font-medium transition-colors duration-300"
                    >
                      <span>Xem tất cả</span>
                      <svg className="w-4 h-4 ml-1 transform group-hover/link:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </Link>
                  </div>
                </div>
                <div className=" dark:bg-gray-800 overflow-y-auto">
                  <div className="max-w-full overflow-x-auto overflow-y-auto">
                    <div className="table-container" style={{ minHeight: '480px' }}>
                      <Table className="overflow-auto">
                      {/* Table Header */}
                      <TableHeader className="bg-gray-50/80 dark:bg-gray-600 border-b border-gray-200 dark:border-gray-600">
                        <TableRow>
                          <TableCell
                            isHeader
                            className="px-4 py-3 font-semibold text-gray-700 text-start text-xs tracking-wide"
                          >
                            <div className="flex items-center gap-2 dark:text-gray-100">
                              <FiEdit3 className="w-3 h-3 text-gray-400 dark:text-gray-100" />
                              Mã yêu cầu
                            </div>
                          </TableCell>
                          <TableCell
                            isHeader
                            className="px-4 py-3 font-semibold text-gray-700 text-start text-xs tracking-wide"
                          >
                            <div className="flex items-center gap-2 dark:text-gray-100">
                              <FiUser className="w-3 h-3 text-gray-400 dark:text-gray-100" />
                              Người yêu cầu
                            </div>
                          </TableCell>
                          <TableCell
                            isHeader
                            className="px-4 py-3 font-semibold text-gray-700 text-start text-xs tracking-wide"
                          >
                            <div className="flex items-center gap-2 dark:text-gray-100">
                              <FiTool className="w-3 h-3 text-gray-400 dark:text-gray-100" />
                              Tool
                            </div>
                          </TableCell>
                          <TableCell
                            isHeader
                            className="px-4 py-3 font-semibold text-gray-700 text-start text-xs tracking-wide"
                          >
                            <div className="flex items-center gap-2 dark:text-gray-100">
                              <FiClock className="w-3 h-3 text-gray-400 dark:text-gray-100" />
                              Trạng thái
                            </div>
                          </TableCell>
                          <TableCell
                            isHeader
                            className="px-4 py-3 font-semibold text-gray-700 text-start text-xs tracking-wide"
                          >
                            <div className="flex items-center gap-2 dark:text-gray-100">
                              <FiCalendar className="w-3 h-3 text-gray-400 dark:text-gray-100" />
                              Ngày tạo
                            </div>
                          </TableCell>
                        </TableRow>
                      </TableHeader>

                      {/* Table Body */}
                      <TableBody className="divide-y divide-gray-50">
                        {loading ? (
                          // Loading skeleton
                          Array.from({ length: 5 }).map((_, index) => (
                            <TableRow key={`skeleton-${index}`} className="animate-pulse">
                              <TableCell className="px-4 py-3">
                                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                              </TableCell>
                              <TableCell className="px-4 py-3">
                                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                              </TableCell>
                              <TableCell className="px-4 py-3">
                                <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                              </TableCell>
                              <TableCell className="px-4 py-3">
                                <div className="h-6 bg-gray-200 rounded-full w-20"></div>
                              </TableCell>
                              <TableCell className="px-4 py-3">
                                <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                              </TableCell>
                            </TableRow>
                          ))
                        ) : (
                          <>
                        {recentTickets.map((ticket) => (
                          <TableRow
                            key={ticket.id}
                            className="group hover:bg-blue-50/50 dark:hover:bg-blue-500/50 cursor-pointer transition-all duration-200 border-b border-gray-100 dark:border-b-blue-light-900 hover:shadow-sm"
                            onClick={() => {
                              console.log('Clicking ticket:', ticket.id);
                              router.push(`/ticket/detail/${ticket.id}`);
                            }}
                          >
                            <TableCell className="px-4 py-3 text-start">
                              <div className="flex items-center gap-2">
                                <div className="w-1.5 h-6 bg-blue-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
                                <div>
                                  <div className="block font-semibold text-gray-900 text-sm group-hover:text-blue-600 transition-colors duration-200 group-hover:underline dark:text-gray-200">
                                    {ticket.title}
                                  </div>
                                  <div className="text-xs text-gray-500 mt-0.5">ID: {ticket.id}</div>
                                </div>
                              </div>
                            </TableCell>
                            <TableCell className="px-4 py-3 text-gray-600 text-start text-sm">
                              <div className="flex items-center gap-2">
                                <div className="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center">
                                  <FiUser className="w-3 h-3 text-gray-500" />
                                </div>
                                <span className="font-medium dark:text-gray-300">{ticket.createdBy || 'Không rõ'}</span>
                              </div>
                            </TableCell>
                            <TableCell className="px-4 py-3 text-gray-600 text-start text-sm">
                              <div className="flex items-center gap-2">
                                <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                                <span className="font-medium dark:text-gray-300">{ticket.moduleName || 'N/A'}</span>
                              </div>
                            </TableCell>
                            <TableCell className="px-4 py-3 text-start">
                              {(() => {
                                const status = ticket.overallStatus || 'Chờ xử lý';
                                const colorClass = statusColor[status] || statusColor['Chờ xử lý'];
                                const icon = statusIcons[status] || statusIcons['Chờ xử lý'];
                                return (
                                  <span className={`inline-flex items-center gap-1.5 px-2.5 py-1 rounded-lg text-xs font-semibold border ${colorClass}`}>
                                    {icon}
                                    {status}
                                  </span>
                                );
                              })()}
                            </TableCell>
                            <TableCell className="px-4 py-3 text-gray-600 text-sm dark:text-gray-300">
                              <div className="flex flex-col">
                                <span className="font-medium">
                                  {new Date(ticket.createdAt).toLocaleDateString('vi-VN')}
                                </span>
                                <span className="text-xs text-gray-400">
                                  {new Date(ticket.createdAt).toLocaleTimeString('vi-VN', {
                                    hour: '2-digit',
                                    minute: '2-digit'
                                  })}
                                </span>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}

                        {/* Render empty rows to maintain consistent height */}
                        {Array.from({ length: Math.max(0, 10 - recentTickets.length) }).map((_, index) => (
                          <TableRow key={`empty-${index}`} className="border-b border-gray-100">
                            <TableCell className="px-4 py-3">&nbsp;</TableCell>
                            <TableCell className="px-4 py-3">&nbsp;</TableCell>
                            <TableCell className="px-4 py-3">&nbsp;</TableCell>
                            <TableCell className="px-4 py-3">&nbsp;</TableCell>
                            <TableCell className="px-4 py-3">&nbsp;</TableCell>
                          </TableRow>
                        ))}
                        </>
                        )}
                      </TableBody>
                    </Table>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* AI Chat - 1 cột */}
            <div className="lg:col-span-1">
              <AIChat className="" />
            </div>
          </div>

          {/* Row 2: Agents gần đây */}
          <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 shadow-sm hover:shadow-lg hover:shadow-sky-100/50 transition-all duration-300 dark:border-gray-600">
            <div className="p-6 border-b border-gray-200 dark:border-gray-600">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold text-sky-700 dark:text-gray-50">Agents Sử Dụng Gần Đây</h2>
                <Link
                  href="/agent"
                  className="text-sky-600 hover:text-sky-700 text-sm font-medium transition-colors duration-300"
                >
                  Xem tất cả →
                </Link>
              </div>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                {recentAgents.map((agent) => (
                  <AgentCard
                    key={agent.id}
                    agent={agent}
                    onClick={() => router.push(`/agent/${agent.code}`)}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}