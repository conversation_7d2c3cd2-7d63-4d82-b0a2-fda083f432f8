import axiosClient from '@/lib/axiosClient';
import { Conversation, ChatMessage, Agent, ParamItem, LoginResponse } from '@/types/index';

export const authApi = {
  async login(code: string, signal?: AbortSignal): Promise<LoginResponse> {
  
    const formData = new FormData();
    formData.append("code", code)
   
    const res = await axiosClient.post('/v1/auth/login-by-iam', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      signal
    });

    return res.data.data;
  },
};