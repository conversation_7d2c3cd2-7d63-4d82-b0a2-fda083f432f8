"use client";

import { authApi } from '@/api/authApi';
import { useAuth } from '@/context/AuthContext';
import { redirectToKeycloakLogin } from '@/utils/auth';
import { redirect, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { FaRobot, FaCog, FaCheckCircle, FaCircleNotch, FaTimesCircle } from 'react-icons/fa';

export default function LoginPage() {
  // const searchParams = useSearchParams();

  // const code = searchParams.get('code')
  const [code, setCode] = useState<string | null>(null);

  const [countdown, setCountdown] = useState(5);
  const [failed, setFailed] = useState(false);
  
  const {login} = useAuth();

  const LoginAsync = async (code: string) => {
    const data = await authApi.login(code)
    if(data)
    {
      login(data.user, data.token)
      redirect('/')
    }
    else{
      setFailed(true)
    }
  }

  // useEffect(() => {
   
  // }, [])
  
  useEffect(() => {
    if(failed){
        const timer = setInterval(() => {
        setCountdown((prev) => {
            if (prev === 1) {
              clearInterval(timer);
              redirectToKeycloakLogin();
            }
            return prev - 1;
        });
        }, 1000);

        return () => clearInterval(timer);
    }
  }, [failed])

  useEffect(() => {
    const progressBar = document.querySelector('.progress-bar-animation') as HTMLElement;
    let width = 0;

    const interval = setInterval(() => {
      width += Math.random() * 20;
      if (width >= 100) {
        width = 100;
        clearInterval(interval);
        const params = new URLSearchParams(window.location.search);
        const c = params.get('code');
        setCode(c);
        if(c)
        {
          LoginAsync(c)
        }else{
          setFailed(true)
        }
      }
      if (progressBar) progressBar.style.width = width + '%';
    }, 300);

    return () => clearInterval(interval);
  }, []);
  
  if(!failed)
    return (
        <div className="bg-gradient-to-br from-blue-50 to-blue-100 min-h-screen flex flex-col items-center justify-center p-4">
        <div className="max-w-lg w-full bg-white rounded-2xl shadow-xl overflow-hidden p-10 text-center transform transition-all hover:shadow-2xl">
            
            <div className="pb-8">
            <div className="relative w-[100px] h-[100px] mx-auto">
                <div className="absolute w-full h-full border-4 border-blue-500/20 rounded-full"></div>
                <div className="absolute w-full h-full border-4 border-t-blue-500 border-transparent rounded-full animate-spin"></div>
                <div className="absolute inset-0 flex items-center justify-center animate-[float_2s_ease-in-out_infinite]">
                <FaRobot className="text-blue-500 text-4xl" />
                </div>
            </div>
            </div>

            <h2 className="text-3xl font-extrabold bg-gradient-to-r from-blue-600 via-blue-500 to-blue-400 bg-clip-text text-transparent mb-3">
            AGENT AI PORTAL
            </h2>
            <h3 className="text-xl font-semibold text-blue-700 mb-6">
            Đang đăng nhập vào hệ thống
            </h3>

            <div className="flex justify-center items-center gap-2 mb-8">
            {[0, 1, 2].map((_, i) => (
                <span
                key={i}
                className="w-2 h-2 rounded-full bg-blue-500 animate-[fade_1.5s_infinite_ease-in-out]"
                style={{ animationDelay: `${i * 0.3}s` }}
                ></span>
            ))}
            </div>

            {/* Status */}
            <div className="text-sm text-blue-400 italic mb-2">
            <FaCog className="inline-block mr-2 animate-spin" />
            Đang kết nối với hệ thống AI...
            </div>

            <div className="text-xs text-blue-300 mt-6 space-y-1">
            <div>
                <FaCheckCircle className="inline-block text-blue-400 mr-1" />
                Đang xác thực danh tính
            </div>
            <div>
                <FaCircleNotch className="inline-block text-blue-400 mr-1 animate-spin" />
                Đang tải các tính năng AI
            </div>
            </div>

            <div className="w-full h-1 bg-blue-200 mt-6 rounded overflow-hidden">
            <div className="progress-bar-animation h-full bg-blue-500 transition-all duration-300 ease-in-out" style={{ width: '0%' }}></div>
            </div>
        </div>
        </div>
    );
    else 
        return (
            <div className="bg-gradient-to-br from-red-50 to-red-100 min-h-screen flex flex-col items-center justify-center p-4">
      <div className="max-w-lg w-full bg-white rounded-2xl shadow-xl overflow-hidden p-10 text-center transform transition-all hover:shadow-2xl">
        <div className="pb-8">
          <div className="relative w-[100px] h-[100px] mx-auto">
            <div className="absolute w-full h-full border-4 border-red-300 rounded-full"></div>
            <div className="absolute w-full h-full border-4 border-t-red-500 border-transparent rounded-full animate-spin"></div>
            <div className="absolute inset-0 flex items-center justify-center animate-[float_2s_ease-in-out_infinite]">
              <FaRobot className="text-red-500 text-4xl" />
              <FaTimesCircle className="absolute bottom-0 right-2 text-red-500 bg-white rounded-full text-xl shadow" />
            </div>
          </div>
        </div>

        <h2 className="text-3xl font-bold text-red-600 mb-3">
          Xác thực thất bại
        </h2>
        <h3 className="text-lg font-semibold text-red-500 mb-6">
          Bạn sẽ bị chuyển hướng trong {countdown}...
        </h3>

        <p className="text-sm text-red-400 italic">
          Vui lòng kiểm tra lại thông tin đăng nhập của bạn.
        </p>
      </div>
    </div>
    )
}
