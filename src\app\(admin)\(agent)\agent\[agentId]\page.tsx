"use client";
import { useEffect, useRef, useState } from 'react';
import ConversationList from '@/components/agent/ConversationList';
import AgentChat from '@/components/agent/AgentChat';
import TicketInfo from '@/components/agent/TicketInfo';
import AgentNavigate from '@/components/agent/AgentNavigate';
import { ChatMessage, Conversation, FileItem, Agent, Ticket, ServiceItem } from '@/types';
import TicketTable from '@/components/ticket/TicketTable';

import FileList from '@/components/file-list/FileList';
import { useParams } from 'next/navigation';
import { useAgent } from '@/hooks/useAgent';
import { conversationApi } from '@/api/conversationApi';
import { ticketApi } from '@/api/ticketApi';
import { agentApi } from '@/api/agentApi';
import Alert from '@/components/ui/alert/Alert';
import { toast } from 'react-toastify';
import TicketTableV2 from '@/components/ticket/TicketTableV2';
import axios from 'axios';
import DynamicForm, { FieldConfig } from '@/components/dynamicform/DynamicForm';

export default function AgentPage() {
    const { agentId } = useParams();
    const { agent } = useAgent(String(agentId))

    return (
        <div className=''>
            <AgentNavigate chatTab={<ConversationTab agent={agent} />} ticketTab={<TicketTab agent={agent}/>}/>
        </div>
    );
}

interface agentProps
{
    agent: Agent | null
}

const ConversationTab: React.FC<agentProps> = ({agent}) => {

    const [files, setFiles] = useState<FileItem[]>([]);

    const [conversation, setConversation] = useState<Conversation[]>([]);
    const [ticket, setTicket] = useState<Ticket | null>(null)
    const [chatLoading, setChatLoading] = useState(false)
    const [messages, setMessages] = useState<ChatMessage[]>([])
    const [select, setSelect] = useState<number>(-1);
    const [isEnabled, setIsEnabled] = useState(false)
    const [services, setServices] = useState<ServiceItem[]>([])
    const [hasMore, setHasMore] = useState<boolean>(false)
    const [conversationLoading, setConversationLoading] = useState(false)
    const [conversationOffset, setConversationOffset] = useState<number>(0)
    const [conversationHasMore, setConversationHasMore] = useState(true);
    // const [parameters, setParameters] = useState<Record<string, string>>({})
    
    
    const parametersRef = useRef<Record<string, string>>({});

    const controller = new AbortController();


    
    useEffect(() => {
        handleSearch('')
    }, [agent])

    const handleSearch = async (text: string) => {
        if (agent == null) return

        setConversationLoading(true)
            try {
                const conversationData = await conversationApi.getConversationsPagination(agent.id, 10, 0, text)
                if (conversationData != null && conversationData.length > 0) {
                    setConversation(conversationData)
                    setConversationOffset(10)
                    setSelect(conversationData[0].id)
                }
            } catch (error) {
                console.error('Error fetching conversations:', error)
            } finally {
                setConversationLoading(false)
            }
    }

    const updateConversationNew = async (id: number) => {
        try{
           const conversationData = await conversationApi.getConversationsDetail(id)
            if (conversationData != null) {
                setConversation(prev => {
                    const updated = [...prev]; 
                    updated[0] = conversationData;
                    return updated;
                });
                setSelect(conversationData.id)
            } 
        }
        catch(err)
        {
            console.log(err)
        }
    }

    const fetchFilesConversation = async (id: number) => {
        try{
           const filesData = await conversationApi.getFilesInConversation(id)
            if (filesData != null) {
                setFiles(filesData)
            } 
        }
        catch(err)
        {
            console.log(err)
        }
    }

    const deleteFilesConversation = async (fileName: string) => {
        try{
            const deletedFile = await conversationApi.deleteFileInConversation(select, fileName)
            if(deletedFile)
            {
                await fetchFilesConversation(select)
            }
        }
        catch(err)
        {
            console.log(err)
        }
    } 
    const generateTicket = async (conversationId: number) => {
        
        toast.success("Ticket đã được tạo thành công!")
        try{
           const conversationData = await conversationApi.getConversationsDetail(select)
            if (conversationData != null) {
                const ticketData = await ticketApi.getTicketDetail(conversationData.ticketId, controller?.signal)
                if (ticketData != null) {
                    setTicket(ticketData)
                }
               return ticket
            } 
        }
        catch(err)
        {
            console.log(err)
        }
    }

    const fetchTicketDetail = async (controller?: AbortController) => {
        if (select == 0 || select == -1)
            return
        const conversationSelected = conversation.filter((value) => value.id == select)
        if (!conversationSelected[0] || conversationSelected[0].ticketId == null || conversationSelected[0].ticketId == 0)
            return
        const ticketData = await ticketApi.getTicketDetail(conversationSelected[0].ticketId, controller?.signal)
        if (ticketData != null) {
            setTicket(ticketData)
        }
    }

    const loadMore = async () => {
        console.log("Load more")
        const firstId = messages[0].id
        try {
            const messageData = await conversationApi.getChatHistory(select, 20, firstId, controller.signal)
            if (messageData != null && messageData.data != null && messageData.data.length > 0) {
                setMessages(prev => [...messageData.data,...prev])
                setHasMore(messageData.hasMore)
            }
        }
        catch(err)
        {}
    }
    
    useEffect(() => {
        setMessages([])
        setTicket(null)
        parametersRef.current = {}


        const fetchHistory = async () => {
            if (select == 0 || select == -1)
                return
            setChatLoading(true)
            try {
                const messageData = await conversationApi.getChatHistory(select, 20, "", controller.signal)
                if (messageData != null && messageData.data != null && messageData.data.length > 0) {
                    setMessages(messageData.data)
                    setHasMore(messageData.hasMore)
                }
            }
            catch(err)
            {}
            
            setChatLoading(false)
        }

        

        const fetchParamsDify = async () => {
            if (select == 0 && agent?.code) {
                const params = await agentApi.getDifyParameters(agent?.code, controller.signal)
                if (params != null) {
                    const selectBoxFields = params
                        .filter(item => {
                            const key = Object.keys(item)[0];
                            return item[key].type === "select";
                        })
                        .map(item => {
                            const key = Object.keys(item)[0];
                            const config = item[key];
                            return {
                                key: config.variable,
                                value: config.default,
                                options: config.options,
                                question: config.label
                            };
                        });
                    console.log(selectBoxFields)
                    setServices(selectBoxFields)
                }
            } else {
                setServices([])
                setIsEnabled(true)
            }
        }

        const fetchParamsConversation = async () => {
            if (select == 0) {
                return
            }
            if (agent?.code) {
                const params = await conversationApi.getConversationParameters(select, controller.signal)
                if (params != null) {
                    params.forEach((value) => {
                        parametersRef.current[value.paramKey] = value.paramValue
                    })
                }
            } else {
                setServices([])
                setIsEnabled(true)
            }
            setChatLoading(false)
        }

        if(select != -1)
        {
            fetchHistory();
            fetchTicketDetail(controller);
            fetchParamsDify();
            fetchParamsConversation();
        }
        
        if(select > 0){
            fetchFilesConversation(select);
        }

        return () => {
            controller.abort();
        };
    }, [select])

    useEffect(() => {
        const totalQuestions = services.length;
        const answeredCount = Object.keys(parametersRef.current).length;

        if (answeredCount < totalQuestions) {
            setIsEnabled(false);
        } else if(conversation.length > 0){
            setIsEnabled(true);
        } 
    }, [services, parametersRef.current])

    const handleNewChat = () => {
        if (conversation.length > 0 && conversation[0].difyId == "")
            return
        setHasMore(false)
        setMessages([])
        setFiles([])
        setConversation([{
            "id": 0,
            "agentId": 1,
            "agentRole": 1,
            "topic": "New Conversation",
            "ticketId": 0,
            "status": "pending",
            "difyId": "",
        }, ...conversation
        ])
        setSelect(0)
    }

    const handleDeleteConversation = async (conversationId: number) => {
        try {
            const success = await conversationApi.deleteConversation(conversationId)
            if (success) {
                setConversation(prev => prev.filter(conv => conv.id !== conversationId))
                if (select === conversationId) {
                    const remainingConversations = conversation.filter(conv => conv.id !== conversationId)
                    if (remainingConversations.length > 0) {
                        setSelect(remainingConversations[0].id)
                    } else {
                        setSelect(-1)
                    }
                }
                toast.success("Đã xóa cuộc trò chuyện thành công!")
            }
        } catch (error) {
            console.error('Error deleting conversation:', error)
        }
    }

    const handleLoadMoreConversations = async () => {
        if (!agent || conversationLoading) return

        setConversationLoading(true)
        try {
            const conversationData = await conversationApi.getConversationsPagination(agent.id, 10, conversationOffset)
            if (conversationData != null && conversationData.length > 0) {
                setConversation(prev => [...prev, ...conversationData])
                setConversationOffset(prev => prev + 10)
            }else{
                setConversationHasMore(false)
            }
        } catch (error) {
            console.error('Error loading more conversations:', error)
        } finally {
            setConversationLoading(false)
        }
    }

    return <div className="flex h-[calc(100vh-180px)] overflow-hidden to-white font-sans gap-4">
        <ConversationList
            conversation={conversation}
            setConversation={setConversation}
            select={select}
            setSelect={setSelect}
            handleNewChat={handleNewChat}
            onDeleteConversation={handleDeleteConversation}
            onLoadMore={handleLoadMoreConversations}
            onSearch={handleSearch}
            hasMore={conversationHasMore}
            loading={conversationLoading}
        />
        {/* Main Chat Area */}
        <AgentChat agent={agent} messages={messages} setMessages={setMessages} files={files} setFiles={setFiles} isEnabled={isEnabled} 
            conversationId={select} updateConversationNew={updateConversationNew} fetchFiles={fetchFilesConversation}
            setIsEnabled={setIsEnabled} listServices={services} loading={chatLoading} agentCode={agent?.code}
            parametersRef={parametersRef} generateTicket={generateTicket} loadMore={loadMore} hasMore={hasMore} isWidget={false}>
            <div className='flex flex-col gap-y-4'>
                {agent?.type.toLowerCase() == "workflow" && <TicketInfo ticketId={ticket?.title} serviceType={ticket?.moduleName} date={ticket?.createdAt} id={ticket?.id}/> }
                <FileList files={files} setFiles={setFiles} deleteFile={deleteFilesConversation} conversationId={select}/>
            </div>
        </AgentChat>
    </div>
}

const TicketTab: React.FC<agentProps> = ({agent}) => {
    return <TicketTableV2 agent={agent}/>

}
