import Link from 'next/link';
import React from 'react';
import styles from '../agent/AgentChat.module.scss';

type TicketInfoProps = {
  ticketId: string | undefined;
  serviceType: string | undefined;
  id: number | undefined;
  // status: 'Open' | 'Closed';
  // priority: 'High' | 'Medium' | 'Low';
  date: string | undefined;
};

const TicketInfo: React.FC<TicketInfoProps> = ({
  ticketId,
  serviceType,
  id,
  // status,
  // priority,
  date
}) => {
  return (
    <div className={`bg-white rounded-xl border border-gray-200/60 hover:border-gray-300/80 transition-all duration-200 ${styles.ticket_card}`}>
      <div className="px-4 py-3 border-b border-gray-100/80">
        <h4 className="font-semibold text-gray-500 text-lg">Thông tin ticket</h4>
      </div>

      <div className="p-4 space-y-4">
        <div className="space-y-3">
          <div className="flex justify-between items-start">
            <span className="text-gray-600 text-xs font-medium uppercase tracking-wide">Ticket ID</span>
            <span className="text-gray-900 font-medium text-sm text-right max-w-[60%] break-words">{ticketId}</span>
          </div>
          <div className="flex justify-between items-start">
            <span className="text-gray-600 text-xs font-medium uppercase tracking-wide">Dịch vụ</span>
            <span className="text-gray-900 text-sm text-right max-w-[60%] break-words">{serviceType}</span>
          </div>
          <div className="flex justify-between items-start">
            <span className="text-gray-600 text-xs font-medium uppercase tracking-wide">Ngày tạo</span>
            <span className="text-gray-900 text-sm text-right max-w-[60%] break-words">{date && new Date(date).toLocaleString()}</span>
          </div>
        </div>

        { id && (
          <div className="pt-3 border-t border-gray-100/80">
            <Link
              className="inline-flex items-center justify-center w-full px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors duration-200"
              href={`/ticket/detail/${id}`}
            >
              Xem chi tiết
            </Link>
          </div>
        )}
      </div>
    </div>
  );
};

export default TicketInfo;