"use client";
import React, { useState } from 'react';
import { FaSearch, FaHeadset, FaPhoneAlt, FaVideo, FaEllipsisV, FaRobot, FaSmile, FaPaperclip, FaPaperPlane, FaTicketAlt, FaUserCircle, FaLightbulb, FaShareSquare, FaHistory, FaBook, FaTools, FaMagic, FaLink, FaRedo, FaPlus, FaTrash, FaExclamation, FaEdit } from 'react-icons/fa';
import { useEffect, useRef } from 'react';
import styles from './ConversationList.module.scss';
import DatePicker from '../form/date-picker';
import { Dropdown } from '../ui/dropdown/Dropdown';
import { DropdownItem } from '../ui/dropdown/DropdownItem';
import { Modal } from '../ui/modal';
import Button from '../ui/button/Button';
import { conversationApi } from '@/api/conversationApi';
import { toast } from 'react-toastify';

interface ConversationListProps {
  conversation: any; // Hoặc thay `any` bằng kiểu dữ liệu cụ thể nếu có, ví dụ: `Ticket | null`
  setConversation: any;
  select: number
  setSelect: React.Dispatch<React.SetStateAction<number>>; // Hoặc `React.Dispatch<React.SetStateAction<Ticket | null>>`
  handleNewChat: () => void
  onDeleteConversation: (conversationId: number) => void
  onLoadMore: () => void
  onSearch: (text: string) => void
  hasMore: boolean
  loading: boolean
}

const ConversationList: React.FC<ConversationListProps> = ({
  conversation,
  setConversation,
  select,
  setSelect,
  handleNewChat,
  onDeleteConversation,
  onLoadMore,
  onSearch,
  hasMore,
  loading
}) => {
  const [search, setSearch] = useState<string>("");
  const [openDropdown, setOpenDropdown] = useState<number | null>(null);
  const scrollRef = useRef<HTMLDivElement>(null);
   const [isOpenOption, setIsOpenOption] = useState(false);
  const [openIndex, setOpenIndex] = useState(-1);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, right: 0, left: 0 });
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showEditModal,setShowEditModal] = useState(false);
  const [titleConversation, setTitleConversation] = useState("")
  const debounceTimeout = useRef<any>(null);
  
  useEffect(() => {
    clearTimeout(debounceTimeout.current);
    debounceTimeout.current = setTimeout(() => {
      onSearch(search)
    }, 1000)
    return () => clearTimeout(debounceTimeout.current);
  }, [search])

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
    if (scrollHeight - scrollTop === clientHeight && hasMore && !loading) {
      onLoadMore();
    }
  };

  const toggleDropdown = (e: any, index: number) => {
      e.preventDefault();
      const rect = e.currentTarget.getBoundingClientRect();
      setDropdownPosition({
        top: rect.top + rect.height,
        right: rect.right,
        left: rect.left
      });
      setOpenIndex(index);
      setIsOpenOption(true);
    };
  
    function closeDropdown() {
      setIsOpenOption(false);
    }
  
    const handleDelete = async () => {
      if (openIndex > -1) {
        try {
          onDeleteConversation(openIndex);
        } catch (err) {
          console.log(err);
        }
        
        setOpenIndex(-1);
        setIsOpenOption(false);
        setShowEditModal(false);
      }
    };

    const handleEditTitle = async () => {
      try{
        const data = await conversationApi.updateConversastion(openIndex, titleConversation)
        if(data){          
          onSearch(search)
          toast.success("Đặt tên thành công!")
        }
      }
      catch(err)
      {
        console.error(err)
      }
      setOpenIndex(-1);
      setIsOpenOption(false);
      setShowEditModal(false);
    }

  return <aside className="w-80 border border-gray-200 bg-white flex flex-col rounded-2xl overflow-hidden shadow-lg shadow-sky-100/50 dark:bg-gray-800 dark:shadow-none dark:border-gray-700">

    <header className="px-6 py-6 bg-sky-50/50 dark:bg-gray-800">
      <div className='flex justify-between gap-2'>
        <div className="relative">
          <input type="text" placeholder="Tìm kiếm trò chuyện..." value={search} className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 transition-all duration-300 dark:text-gray-400 dark:border-gray-700 dark:bg-gray-900"
            onChange={(e) => setSearch(e.target.value)} />

          <FaSearch className="absolute left-3 top-3 dark:text-gray-100" />
        </div>
        <div className='flex gap-2 justify-between items-center cursor-pointer text-sky-700 hover:text-sky-600 transition-colors duration-300' onClick={handleNewChat}>
          <p className='font-bold'>CHAT</p>
          <FaPlus />
        </div>
      </div>
      {/* <div className='flex border-none gap-2 mt-2'>
        <div className='rounded-2xl px-2 bg-gray-300 cursor-pointer'>
          All (12)
        </div>
        <div className='rounded-2xl px-2 bg-gray-300 cursor-pointer'>
          Unread (2)
        </div>
      </div> */}
    </header>

    {/* Conversation list */}
    <section
      ref={scrollRef}
      className={`flex-1 overflow-y-auto ${styles.custom_scrollbar} divide-y divide-gray-200 gap-2 px-4 flex flex-col `}
      onScroll={handleScroll}
    >
      {
        conversation.map((item: any, index: number) => {
          return <div key={index} className={`p-4 border rounded-lg hover:bg-sky-50 cursor-pointer transition-all duration-100 dark:border-gray-700 ${item.id == select && 'border-l-4 border-blue-600 bg-sky-50 shadow-md shadow-sky-100 dark:shadow-none dark:bg-gray-700/80'}`} onClick={() => setSelect(item.id)}>
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <h3 className="font-medium text-gray-800 dark:text-gray-300">{item.topic}</h3>
                <p className="text-sm text-gray-500">{item.ticketId ? `Ticket #${item.ticketId}` : "Chưa tạo ticket"}</p>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-xs text-gray-500">{item.time}</span>
                {item.id !== 0 && (
                  <div className="relative">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleDropdown(e, item.id);
                      }}
                      className="p-1 hover:bg-gray-200 rounded-full transition-colors duration-200"
                    >
                      <FaEllipsisV className="text-gray-400 text-xs" />
                    </button>
                  </div>
                )}
              </div>
            </div>
            <p className="mt-1 text-sm text-gray-700 truncate">{item.message}</p>
            <div className="mt-2 flex items-center">
              <span
                className={`inline-block h-2 w-2 rounded-full mr-2 ${item.status === 'open' ? 'bg-green-500' : item.status == 'pending' ? 'bg-sky-500' : 'bg-gray-500'
                  }`}
              ></span>
              <span className="text-xs text-gray-500 capitalize">{item.status}</span>
            </div>
          </div>
        })
      }

      {/* Loading indicator */}
      {loading && (
        <div className="flex justify-center py-4">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-sky-500"></div>
        </div>
      )}

      {/* Load more indicator */}
      {hasMore && !loading && (
        <div className="text-center py-4 text-sm text-gray-500">
          Kéo xuống để tải thêm...
        </div>
      )}
      <Dropdown
          isOpen={isOpenOption}
          onClose={closeDropdown}
          className="fixed right-0 top-0 flex w-[300px] flex-col rounded-2xl border border-gray-200 bg-white p-3 shadow-lg dark:border-gray-800 dark:bg-gray-dark"
          style={{
            top: dropdownPosition.top,
            left: dropdownPosition.right,
          }}
        >
          <ul className="flex flex-col gap-1 py-2">

            <li>
              <DropdownItem
                onItemClick={() => setShowEditModal(true)}
                tag="a"
                className="flex items-center gap-3 px-3 py-2 font-medium text-gray-700 rounded-lg group text-theme-sm hover:bg-red-50 hover:text-red-600 transition-all duration-300"
              >
                <FaEdit/>
                Sửa tên của cuộc trò chuyện
              </DropdownItem>
              <DropdownItem
                onItemClick={() => setShowDeleteModal(true)}
                tag="a"
                className="flex items-center gap-3 px-3 py-2 font-medium text-gray-700 rounded-lg group text-theme-sm hover:bg-red-50 hover:text-red-600 transition-all duration-300"
              >
                <FaTrash />
                Xóa
              </DropdownItem>
            </li>
          </ul>
        </Dropdown>
        {showDeleteModal && (
        <Modal isOpen={showDeleteModal} onClose={() => setShowDeleteModal(false)} className="max-w-[560px]">
          <div className="mt-6 p-6 w-full text-center">
            <div className="mb-4">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <FaExclamation className="text-red-600 text-2xl" />
              </div>
            </div>
            <h3 className="text-lg font-semibold text-gray-800 mb-2">Xác nhận xóa</h3>
            <p className="mb-6 text-gray-600">Bạn có chắc chắn muốn xóa cuộc trò chuyện này chứ? <i>(Toàn bộ lịch sử trò chuyện sẽ bị mất)</i></p>
            <div className="flex justify-center space-x-4">
              <Button variant="outline" onClick={() => setShowDeleteModal(false)}>
                Hủy bỏ
              </Button>
              <Button
                onClick={handleDelete}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                Đồng ý
              </Button>
            </div>
          </div>
        </Modal>
      )}
      {showEditModal && (
        <Modal isOpen={showEditModal} onClose={() => setShowEditModal(false)} className="max-w-[560px]">
          <div className="mt-6 p-6 w-full text-center">
            <div className="mb-4">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <FaEdit className="text-blue-600 text-2xl" />
              </div>
            </div>
            <h3 className="text-lg font-semibold text-gray-800 mb-2">Chỉnh sửa tên cuộc trò chuyện</h3>
            <p className="mb-6 text-gray-600">
              Đặt lại tên để dễ dàng quản lý các cuộc trò chuyện của bạn.
            </p>
            <input
              type="text"
              className="w-full border border-gray-300 rounded-lg px-4 py-2 mb-6 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Nhập tên mới..."
              value={titleConversation}
              onChange={e => setTitleConversation(e.target.value)}
              maxLength={60}
            />
            <div className="flex justify-center space-x-4">
              <Button variant="outline" onClick={() => setShowEditModal(false)}>
                Hủy bỏ
              </Button>
              <Button
                onClick={handleEditTitle}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
                // disabled={!name.trim()}
              >
                Lưu thay đổi
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </section>
  </aside>
}

export default ConversationList;